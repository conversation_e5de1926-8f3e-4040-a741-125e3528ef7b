#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取NavyAily用户所有作品标题的脚本
"""

import json
import time
import requests
from apiproxy.douyin.douyin import Douyin
from apiproxy.douyin import douyin_headers
from apiproxy.common import utils
from rich.console import Console
from rich.table import Table

def get_all_titles():
    """获取NavyAily用户的所有作品标题"""
    console = Console()
    
    # NavyAily的用户ID
    sec_uid = "MS4wLjABAAAA7fD5NjzXIaqy25PZj6aTbasc2NilKMnf_RhInATdnnIdIESLruwwrisCyB8pZ36y"
    
    # 初始化Douyin对象
    dy = Douyin()
    
    console.print("[cyan]🔍 正在获取NavyAily的所有作品标题...[/]")
    
    # 获取用户信息
    try:
        aweme_list = dy.getUserInfo(sec_uid=sec_uid, mode="post", count=35, number=0)
        
        if not aweme_list:
            console.print("[red]❌ 无法获取用户作品列表[/]")
            return
            
        console.print(f"[green]✅ 成功获取到 {len(aweme_list)} 个作品[/]")
        
        # 创建表格显示作品信息
        table = Table(title="NavyAily 作品列表")
        table.add_column("序号", style="cyan", no_wrap=True)
        table.add_column("发布时间", style="magenta")
        table.add_column("作品ID", style="yellow")
        table.add_column("标题/描述", style="green")
        table.add_column("类型", style="blue")
        
        # 遍历作品列表
        for i, aweme in enumerate(aweme_list, 1):
            # 获取作品信息
            aweme_id = aweme.get('aweme_id', 'N/A')
            desc = aweme.get('desc', '无标题')
            create_time = aweme.get('create_time', 0)
            
            # 转换时间戳为可读格式
            if create_time:
                create_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(create_time)))
            else:
                create_time_str = 'N/A'
            
            # 判断作品类型
            if aweme.get('images'):
                aweme_type = "图集"
            else:
                aweme_type = "视频"
            
            # 添加到表格
            table.add_row(
                str(i),
                create_time_str,
                aweme_id,
                desc[:50] + "..." if len(desc) > 50 else desc,
                aweme_type
            )
            
            # 检查是否是目标作品
            if aweme_id == "7529338334209887538":
                console.print(f"[red]🎯 找到目标作品！序号: {i}[/]")
                console.print(f"[yellow]标题: {desc}[/]")
                console.print(f"[yellow]类型: {aweme_type}[/]")
        
        # 显示表格
        console.print(table)
        
        # 保存到文件
        titles_data = []
        for i, aweme in enumerate(aweme_list, 1):
            titles_data.append({
                "序号": i,
                "作品ID": aweme.get('aweme_id', 'N/A'),
                "标题": aweme.get('desc', '无标题'),
                "发布时间": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(aweme.get('create_time', 0)))) if aweme.get('create_time') else 'N/A',
                "类型": "图集" if aweme.get('images') else "视频"
            })
        
        # 保存为JSON文件
        with open('NavyAily_作品标题列表.json', 'w', encoding='utf-8') as f:
            json.dump(titles_data, f, ensure_ascii=False, indent=2)
        
        console.print(f"[green]✅ 作品标题列表已保存到: NavyAily_作品标题列表.json[/]")
        
    except Exception as e:
        console.print(f"[red]❌ 获取作品列表失败: {str(e)}[/]")

if __name__ == "__main__":
    get_all_titles()
